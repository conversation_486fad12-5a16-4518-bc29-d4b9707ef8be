#!/usr/bin/env python3
"""
局域网设备连接测试脚本
测试指定IP地址列表中设备的网络连接状态
"""

import subprocess
import sys
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# 从图片中提取的设备IP地址列表
DEVICES = {
    "GZ-home-openwrt": "**********",
    "Mac Mini M2": "**********31", 
    "NAS-VM-***********-51": "**********3",
    "Henry-home-openwrt": "**********",
    "GPU-server-22": "**********2",
    "Macbook M2": "**********30",
    "1603-openwrt": "**********",
    "GPU-server-docker1079": "**********1",
    "GPU-server-docker1888": "**********0",
    "SZ-home-openwrt": "**********"
}

def ping_device(device_name, ip_address, count=4, timeout=3):
    """
    使用ping命令测试单个设备的连接性
    
    Args:
        device_name: 设备名称
        ip_address: IP地址
        count: ping次数
        timeout: 超时时间(秒)
    
    Returns:
        dict: 包含测试结果的字典
    """
    try:
        # 根据操作系统选择ping命令参数
        if sys.platform.startswith('win'):
            cmd = ['ping', '-n', str(count), '-w', str(timeout * 1000), ip_address]
        else:
            cmd = ['ping', '-c', str(count), '-W', str(timeout), ip_address]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 5)
        end_time = time.time()
        
        success = result.returncode == 0
        response_time = round((end_time - start_time) * 1000, 2)  # 转换为毫秒
        
        # 解析ping输出获取更详细的信息
        output_lines = result.stdout.split('\n')
        packet_loss = "Unknown"
        avg_time = "Unknown"
        
        for line in output_lines:
            if 'packet loss' in line.lower() or '丢失' in line:
                # 提取丢包率
                if '%' in line:
                    parts = line.split('%')
                    if len(parts) > 0:
                        loss_part = parts[0].split()[-1]
                        try:
                            packet_loss = f"{loss_part}%"
                        except:
                            pass
            elif 'avg' in line.lower() or '平均' in line:
                # 提取平均响应时间
                if 'ms' in line:
                    parts = line.split('/')
                    if len(parts) >= 4:
                        try:
                            avg_time = f"{float(parts[4]):.2f}ms"
                        except:
                            pass
        
        return {
            'device_name': device_name,
            'ip_address': ip_address,
            'status': 'ONLINE' if success else 'OFFLINE',
            'response_time': response_time,
            'packet_loss': packet_loss,
            'avg_ping_time': avg_time,
            'raw_output': result.stdout if success else result.stderr
        }
        
    except subprocess.TimeoutExpired:
        return {
            'device_name': device_name,
            'ip_address': ip_address,
            'status': 'TIMEOUT',
            'response_time': timeout * 1000,
            'packet_loss': "100%",
            'avg_ping_time': "Timeout",
            'raw_output': f"Ping timeout after {timeout} seconds"
        }
    except Exception as e:
        return {
            'device_name': device_name,
            'ip_address': ip_address,
            'status': 'ERROR',
            'response_time': 0,
            'packet_loss': "Unknown",
            'avg_ping_time': "Error",
            'raw_output': str(e)
        }

def test_port_connectivity(ip_address, port, timeout=3):
    """
    测试特定端口的连接性
    
    Args:
        ip_address: IP地址
        port: 端口号
        timeout: 超时时间
    
    Returns:
        bool: 端口是否可达
    """
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip_address, port))
        sock.close()
        return result == 0
    except:
        return False

def print_results(results):
    """
    格式化打印测试结果
    """
    print("\n" + "="*80)
    print(f"网络连接测试报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 统计信息
    online_count = sum(1 for r in results if r['status'] == 'ONLINE')
    offline_count = len(results) - online_count
    
    print(f"总设备数: {len(results)}")
    print(f"在线设备: {online_count}")
    print(f"离线设备: {offline_count}")
    print(f"连接成功率: {(online_count/len(results)*100):.1f}%")
    print("\n" + "-"*80)
    
    # 详细结果
    print(f"{'设备名称':<25} {'IP地址':<15} {'状态':<10} {'响应时间':<12} {'丢包率':<10}")
    print("-"*80)
    
    # 按状态排序，在线设备在前
    sorted_results = sorted(results, key=lambda x: (x['status'] != 'ONLINE', x['device_name']))
    
    for result in sorted_results:
        status_color = ""
        if result['status'] == 'ONLINE':
            status_color = "✅"
        elif result['status'] == 'OFFLINE':
            status_color = "❌"
        else:
            status_color = "⚠️"
            
        print(f"{result['device_name']:<25} {result['ip_address']:<15} "
              f"{status_color} {result['status']:<8} {result['avg_ping_time']:<12} {result['packet_loss']:<10}")

def main():
    """
    主函数：执行网络连接测试
    """
    print("开始测试局域网设备连接状态...")
    print(f"测试设备数量: {len(DEVICES)}")
    
    # 使用线程池并发测试以提高效率
    results = []
    with ThreadPoolExecutor(max_workers=10) as executor:
        # 提交所有ping任务
        future_to_device = {
            executor.submit(ping_device, name, ip): (name, ip) 
            for name, ip in DEVICES.items()
        }
        
        # 收集结果
        for future in as_completed(future_to_device):
            device_name, ip = future_to_device[future]
            try:
                result = future.result()
                results.append(result)
                # 实时显示进度
                status_symbol = "✅" if result['status'] == 'ONLINE' else "❌"
                print(f"{status_symbol} {device_name} ({ip}) - {result['status']}")
            except Exception as e:
                print(f"❌ {device_name} ({ip}) - 测试出错: {e}")
                results.append({
                    'device_name': device_name,
                    'ip_address': ip,
                    'status': 'ERROR',
                    'response_time': 0,
                    'packet_loss': "Unknown",
                    'avg_ping_time': "Error",
                    'raw_output': str(e)
                })
    
    # 打印汇总报告
    print_results(results)
    
    # 可选：测试常见服务端口
    print("\n" + "="*80)
    print("测试常见服务端口连接性...")
    print("="*80)
    
    common_ports = [22, 80, 443, 8080]  # SSH, HTTP, HTTPS, 备用HTTP
    
    for result in results:
        if result['status'] == 'ONLINE':
            print(f"\n{result['device_name']} ({result['ip_address']}):")
            for port in common_ports:
                is_open = test_port_connectivity(result['ip_address'], port, timeout=2)
                status = "开放" if is_open else "关闭"
                symbol = "🟢" if is_open else "🔴"
                print(f"  {symbol} 端口 {port}: {status}")

if __name__ == "__main__":
    main()
